/**
 * 测试插件系统的脚本
 * 可以在浏览器控制台中运行
 */

// 测试插件加载
async function testPluginSystem() {
  console.log('开始测试插件系统...');
  
  try {
    // 检查插件管理器是否存在
    if (!window.pluginManager) {
      console.error('插件管理器未找到');
      return;
    }
    
    console.log('插件管理器已找到:', window.pluginManager);
    
    // 加载插件
    console.log('正在加载插件...');
    const result = await window.pluginManager.loadPlugin('http://localhost:3001/plugin.js');
    
    if (result.success) {
      console.log('插件加载成功:', result.plugin);
      
      // 激活插件
      console.log('正在激活插件...');
      const activated = await window.pluginManager.activatePlugin(result.plugin.name);
      
      if (activated) {
        console.log('插件激活成功');
        
        // 检查注册的组件
        const components = window.pluginManager.getAllComponents();
        console.log('已注册的组件:', components);
        
        // 检查插件状态
        const plugins = window.pluginManager.getAllPlugins();
        console.log('所有插件:', plugins);
        
        console.log('插件系统测试完成！');
      } else {
        console.error('插件激活失败');
      }
    } else {
      console.error('插件加载失败:', result.error);
    }
  } catch (error) {
    console.error('测试过程中出现错误:', error);
  }
}

// 导出测试函数
if (typeof window !== 'undefined') {
  window.testPluginSystem = testPluginSystem;
}

console.log('测试脚本已加载，请在浏览器控制台中运行 testPluginSystem() 来测试插件系统');
