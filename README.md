# React + Vite 插件系统

这是一个基于 React 和 Vite 的可扩展插件系统，支持动态加载和管理插件。

## 项目结构

```
.
├── main-app/                 # 主应用
│   ├── src/
│   │   ├── plugin-system/    # 插件系统核心
│   │   ├── components/       # 主应用组件
│   │   └── ...
│   ├── public/
│   └── package.json
├── example-plugin/           # 示例插件
│   ├── src/
│   │   └── plugin.js        # 插件源码
│   ├── dist/                # 构建输出
│   └── package.json
└── README.md
```

## 特性

- ✅ **ESM 模块支持**: 主应用和插件都构建为 ES 模块
- ✅ **共享依赖**: React 等库不会重复打包，由主应用提供
- ✅ **动态加载**: 支持运行时动态加载插件
- ✅ **组件注册**: 插件可以注册 React 组件到主应用
- ✅ **事件系统**: 插件与主应用间的事件通信
- ✅ **生命周期管理**: 插件激活/停用管理
- ✅ **本地存储**: 插件数据持久化

## 快速开始

### 1. 启动主应用

```bash
cd main-app
bun install
bun run dev
```

主应用将在 http://localhost:3000 启动

### 2. 启动插件服务器

```bash
cd example-plugin
bun install
bun run build
bun run serve
```

插件服务器将在 http://localhost:3001 启动

### 3. 测试插件系统

1. 打开浏览器访问 http://localhost:3000
2. 在页面的插件演示区域，输入插件 URL: `http://localhost:3001/plugin.js`
3. 点击"加载插件"按钮
4. 插件将被加载并自动激活，您将看到插件组件显示在页面上

## 插件开发

### 插件结构

一个插件必须导出一个包含以下属性的对象：

```javascript
const plugin = {
  name: 'plugin-name',           // 插件名称（必需）
  version: '1.0.0',              // 插件版本（必需）
  description: 'Plugin description', // 插件描述
  
  // 插件激活函数（必需）
  async activate(context) {
    // 插件初始化逻辑
    context.registerComponent('MyComponent', MyComponent);
  },
  
  // 插件停用函数（可选）
  async deactivate(context) {
    // 清理逻辑
  }
};

export default plugin;
```

### 插件上下文 API

插件的 `activate` 和 `deactivate` 函数会接收一个上下文对象，包含以下 API：

- `context.react` - React 库引用
- `context.reactDOM` - ReactDOM 库引用
- `context.registerComponent(name, component)` - 注册组件
- `context.registerRoute(path, component)` - 注册路由
- `context.emit(event, ...args)` - 发送事件
- `context.on(event, listener)` - 监听事件
- `context.off(event, listener)` - 取消监听
- `context.storage` - 本地存储 API
- `context.apiVersion` - 插件 API 版本

### 插件构建配置

插件的 `vite.config.js` 需要配置外部依赖：

```javascript
export default defineConfig({
  build: {
    lib: {
      entry: 'src/plugin.js',
      formats: ['es']
    },
    rollupOptions: {
      external: ['react', 'react-dom'], // 标记为外部依赖
      output: {
        format: 'es'
      }
    }
  }
});
```

## 技术实现

### 共享依赖机制

1. **主应用**: 将 React 等库通过插件上下文提供给插件
2. **插件构建**: 将共享库标记为 `external`，不打包到插件中
3. **运行时**: 插件通过上下文获取共享库的引用

### 插件加载流程

1. 使用动态 `import()` 加载插件模块
2. 验证插件结构和必需属性
3. 创建插件上下文，提供 API 和共享库
4. 调用插件的 `activate` 函数
5. 管理插件状态和生命周期

## 开发工具

### 浏览器调试

插件管理器挂载在 `window.pluginManager`，可以在浏览器控制台中调试：

```javascript
// 查看所有插件
window.pluginManager.getAllPlugins()

// 查看所有组件
window.pluginManager.getAllComponents()

// 手动加载插件
await window.pluginManager.loadPlugin('http://localhost:3001/plugin.js')
```

### 测试脚本

运行测试脚本验证插件系统：

```javascript
// 在浏览器控制台中运行
testPluginSystem()
```

## 注意事项

1. **Node.js 版本**: 确保使用 Node.js 18+ 版本
2. **CORS**: 插件服务器需要支持 CORS
3. **ESM**: 所有模块都使用 ES 模块格式
4. **共享依赖**: 插件不应该打包主应用已有的依赖

## 扩展功能

这个插件系统可以进一步扩展：

- 路由系统集成
- 插件权限管理
- 插件市场和版本管理
- 热重载支持
- TypeScript 支持
- 插件间通信机制
