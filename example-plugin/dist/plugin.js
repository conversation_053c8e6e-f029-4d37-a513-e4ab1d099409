let e;
function r() {
  const [n, t] = e.useState(0), [o, i] = e.useState("Hello from Plugin!");
  e.useEffect(() => (console.log("Plugin component mounted"), () => {
    console.log("Plugin component unmounted");
  }), []);
  const a = () => {
    t((l) => l + 1), i(`Clicked ${n + 1} times from plugin!`);
  };
  return e.createElement("div", {
    style: {
      padding: "20px",
      backgroundColor: "#e3f2fd",
      borderRadius: "8px",
      border: "2px solid #2196f3"
    }
  }, [
    e.createElement("h3", { key: "title" }, "Example Plugin Component"),
    e.createElement("p", { key: "message" }, o),
    e.createElement("button", {
      key: "button",
      onClick: a,
      style: {
        padding: "8px 16px",
        backgroundColor: "#2196f3",
        color: "white",
        border: "none",
        borderRadius: "4px",
        cursor: "pointer"
      }
    }, `Click me! (${n})`),
    e.createElement("div", {
      key: "info",
      style: { marginTop: "10px", fontSize: "0.9em", color: "#666" }
    }, "This component is loaded from an external plugin!")
  ]);
}
function s() {
  return e.createElement("div", {
    style: {
      padding: "15px",
      backgroundColor: "#f3e5f5",
      borderRadius: "8px",
      border: "2px solid #9c27b0",
      marginTop: "10px"
    }
  }, [
    e.createElement("h4", { key: "title" }, "Plugin Information"),
    e.createElement("ul", { key: "list" }, [
      e.createElement("li", { key: "name" }, "Name: Example Plugin"),
      e.createElement("li", { key: "version" }, "Version: 1.0.0"),
      e.createElement("li", { key: "author" }, "Author: Plugin Developer"),
      e.createElement("li", { key: "description" }, "Description: A simple example plugin for demonstration")
    ])
  ]);
}
const p = {
  name: "example-plugin",
  version: "1.0.0",
  description: "A simple example plugin that demonstrates the plugin system",
  author: "Plugin Developer",
  /**
   * 插件激活函数
   * @param {Object} context - 插件上下文，由主应用提供
   */
  async activate(n) {
    if (console.log("Example plugin activating...", n), e = n.react, !e)
      throw new Error("React is not available in plugin context");
    n.registerComponent("HelloWorld", r), n.registerComponent("PluginInfo", s), n.on("app:ready", () => {
      console.log("Main app is ready, plugin can now interact with it");
    }), n.emit("plugin:ready", {
      pluginName: this.name,
      version: this.version
    }), n.storage.set(`${this.name}_activated_at`, (/* @__PURE__ */ new Date()).toISOString()), n.storage.set(
      `${this.name}_activation_count`,
      (n.storage.get(`${this.name}_activation_count`) || 0) + 1
    ), console.log("Example plugin activated successfully");
  },
  /**
   * 插件停用函数（可选）
   * @param {Object} context - 插件上下文
   */
  async deactivate(n) {
    console.log("Example plugin deactivating..."), n.storage.set(`${this.name}_deactivated_at`, (/* @__PURE__ */ new Date()).toISOString()), n.emit("plugin:deactivated", {
      pluginName: this.name,
      version: this.version
    }), console.log("Example plugin deactivated");
  }
};
export {
  p as default
};
