/*
  @license
	Rollup.js v4.50.0
	Sun, 31 Aug 2025 07:37:46 GMT - commit 592e7d78f726bb038881f3c9ab4450d26c3db8c7

	https://github.com/rollup/rollup

	Released under the MIT License.
*/
'use strict';

Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });

require('./native.js');
const parseAst_js = require('./shared/parseAst.js');
require('node:path');



exports.parseAst = parseAst_js.parseAst;
exports.parseAstAsync = parseAst_js.parseAstAsync;
//# sourceMappingURL=parseAst.js.map
