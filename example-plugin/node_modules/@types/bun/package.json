{"name": "@types/bun", "version": "1.2.21", "description": "TypeScript definitions for bun", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/bun", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/Jar<PERSON>-<PERSON>"}, {"name": "Rob<PERSON>un", "githubUsername": "robobun", "url": "https://github.com/robobun"}, {"name": "<PERSON>", "githubUsername": "dylan-conway", "url": "https://github.com/dylan-conway"}, {"name": "<PERSON><PERSON>", "githubUsername": "nektro", "url": "https://github.com/nektro"}, {"name": "<PERSON>", "githubUsername": "RiskyMH", "url": "https://github.com/RiskyMH"}, {"name": "<PERSON>", "githubUsername": "alii", "url": "https://github.com/alii"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/bun"}, "scripts": {}, "dependencies": {"bun-types": "1.2.21"}, "peerDependencies": {}, "typesPublisherContentHash": "95edf465084e25778ca108276e8cf5eade636e3fed1e56e4e91d1d0cfabf74b3", "typeScriptVersion": "5.2"}