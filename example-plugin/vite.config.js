import { defineConfig } from 'vite'

export default defineConfig({
  build: {
    lib: {
      entry: 'src/plugin.js',
      name: 'ExamplePlugin',
      fileName: 'plugin',
      formats: ['es']
    },
    rollupOptions: {
      // 将 React 和 ReactDOM 标记为外部依赖，不打包到插件中
      external: ['react', 'react-dom'],
      output: {
        // 告诉 Rollup 如何在 UMD 构建中为这些外部化的依赖提供全局变量
        globals: {
          'react': 'window.__SHARED_LIBS__.react',
          'react-dom': 'window.__SHARED_LIBS__.reactDOM'
        },
        // 确保输出为 ES 模块格式
        format: 'es'
      }
    },
    // 输出目录
    outDir: 'dist',
    // 清空输出目录
    emptyOutDir: true
  },
  // 开发服务器配置
  server: {
    port: 3001,
    cors: true
  },
  // 预览服务器配置
  preview: {
    port: 3001,
    cors: true
  }
})
