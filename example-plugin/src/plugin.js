/**
 * 示例插件
 * 演示如何创建一个简单的插件，包含组件注册和事件处理
 */

// 从全局共享库获取 React（在运行时由主应用提供）
// 注意：这里我们使用动态导入来获取共享的 React
let React;

// 插件组件
function HelloWorldComponent() {
  const [count, setCount] = React.useState(0);
  const [message, setMessage] = React.useState('Hello from Plugin!');

  React.useEffect(() => {
    console.log('Plugin component mounted');
    return () => {
      console.log('Plugin component unmounted');
    };
  }, []);

  const handleClick = () => {
    setCount(prev => prev + 1);
    setMessage(`Clicked ${count + 1} times from plugin!`);
  };

  return React.createElement('div', {
    style: {
      padding: '20px',
      backgroundColor: '#e3f2fd',
      borderRadius: '8px',
      border: '2px solid #2196f3'
    }
  }, [
    React.createElement('h3', { key: 'title' }, 'Example Plugin Component'),
    React.createElement('p', { key: 'message' }, message),
    React.createElement('button', {
      key: 'button',
      onClick: handleClick,
      style: {
        padding: '8px 16px',
        backgroundColor: '#2196f3',
        color: 'white',
        border: 'none',
        borderRadius: '4px',
        cursor: 'pointer'
      }
    }, `Click me! (${count})`),
    React.createElement('div', {
      key: 'info',
      style: { marginTop: '10px', fontSize: '0.9em', color: '#666' }
    }, 'This component is loaded from an external plugin!')
  ]);
}

// 另一个插件组件
function PluginInfoComponent() {
  return React.createElement('div', {
    style: {
      padding: '15px',
      backgroundColor: '#f3e5f5',
      borderRadius: '8px',
      border: '2px solid #9c27b0',
      marginTop: '10px'
    }
  }, [
    React.createElement('h4', { key: 'title' }, 'Plugin Information'),
    React.createElement('ul', { key: 'list' }, [
      React.createElement('li', { key: 'name' }, 'Name: Example Plugin'),
      React.createElement('li', { key: 'version' }, 'Version: 1.0.0'),
      React.createElement('li', { key: 'author' }, 'Author: Plugin Developer'),
      React.createElement('li', { key: 'description' }, 'Description: A simple example plugin for demonstration')
    ])
  ]);
}

// 插件主对象
const plugin = {
  name: 'example-plugin',
  version: '1.0.0',
  description: 'A simple example plugin that demonstrates the plugin system',
  author: 'Plugin Developer',

  /**
   * 插件激活函数
   * @param {Object} context - 插件上下文，由主应用提供
   */
  async activate(context) {
    console.log('Example plugin activating...', context);
    
    // 获取 React 引用
    React = context.react;
    
    if (!React) {
      throw new Error('React is not available in plugin context');
    }

    // 注册组件到主应用
    context.registerComponent('HelloWorld', HelloWorldComponent);
    context.registerComponent('PluginInfo', PluginInfoComponent);

    // 监听主应用事件
    context.on('app:ready', () => {
      console.log('Main app is ready, plugin can now interact with it');
    });

    // 发送插件就绪事件
    context.emit('plugin:ready', {
      pluginName: this.name,
      version: this.version
    });

    // 存储一些插件数据
    context.storage.set(`${this.name}_activated_at`, new Date().toISOString());
    context.storage.set(`${this.name}_activation_count`, 
      (context.storage.get(`${this.name}_activation_count`) || 0) + 1
    );

    console.log('Example plugin activated successfully');
  },

  /**
   * 插件停用函数（可选）
   * @param {Object} context - 插件上下文
   */
  async deactivate(context) {
    console.log('Example plugin deactivating...');
    
    // 清理资源
    context.storage.set(`${this.name}_deactivated_at`, new Date().toISOString());
    
    // 发送插件停用事件
    context.emit('plugin:deactivated', {
      pluginName: this.name,
      version: this.version
    });

    console.log('Example plugin deactivated');
  }
};

// 导出插件
export default plugin;
